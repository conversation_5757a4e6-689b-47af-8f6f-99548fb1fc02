package com.jarvis.appium.examples;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.concurrent.TimeUnit;

/**
 * 🎯 COMPREHENSIVE DEMO TEST CASE
 * 
 * This demo showcases the complete MCP integration capabilities:
 * 1. ✅ MCP Server Connection
 * 2. 📱 Android App Launch
 * 3. 🔍 AI-Powered Element Discovery
 * 4. 🛒 E-commerce Shopping Flow
 * 5. 🔐 User Authentication
 * 6. 📸 Visual Verification
 * 
 * Perfect for demonstrating the power of Given-When-Then automation!
 */
public class DemoTestCase {
    
    private static final Logger logger = LoggerFactory.getLogger(DemoTestCase.class);
    
    // Configuration
    private static final String APK_PATH = "C:\\POC\\AI\\MCP Integration Server\\mcp-client-integration\\Android.SauceLabs.Mobile.Sample.app.2.7.1.apk";
    private static final String PACKAGE_NAME = "com.swaglabsmobileapp";
    private static final String ACTIVITY_NAME = "com.swaglabsmobileapp.SplashActivity";
    
    private static BufferedWriter writer;
    private static BufferedReader reader;
    private static BufferedReader errorReader;
    private static int requestId = 1;
    
    public static void main(String[] args) {
        try {
            logger.info("🎬 STARTING COMPREHENSIVE DEMO TEST CASE");
            logger.info("==========================================");
            logger.info("🎯 This demo will showcase:");
            logger.info("   ✅ MCP Server Connection");
            logger.info("   📱 Android App Launch");
            logger.info("   🔍 AI-Powered Element Discovery");
            logger.info("   🛒 Complete Shopping Flow");
            logger.info("   🔐 User Authentication");
            logger.info("   📸 Visual Verification");
            logger.info("");
            
            // Initialize MCP connection
            initializeMcp();
            
            // Run comprehensive demo scenarios
            runDemoScenarios();
            
            logger.info("");
            logger.info("🎉 DEMO COMPLETED SUCCESSFULLY!");
            logger.info("✨ All features demonstrated successfully!");
            
        } catch (Exception e) {
            logger.error("❌ Demo failed", e);
        }
    }
    
    private static void initializeMcp() throws IOException, InterruptedException {
        logger.info("🚀 PHASE 1: Initializing MCP Connection");
        logger.info("=====================================");
        
        // Start MCP server
        String[] command = {"node", "dist/index.js"};
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.directory(new File("C:\\POC\\AI\\MCP Integration Server\\jarvis-appium"));
        processBuilder.redirectErrorStream(false);
        
        Process process = processBuilder.start();
        
        writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
        reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        
        // Start output readers
        startOutputReaders();
        Thread.sleep(3000);
        
        // Initialize MCP
        logger.info("🔗 Establishing MCP connection...");
        sendMcpRequest("initialize", "{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"demo-test-case\",\"version\":\"1.0.0\"},\"capabilities\":{}}");
        Thread.sleep(2000);
        
        // List available tools
        logger.info("🛠️ Discovering available tools...");
        sendMcpRequest("tools/list", "{}");
        Thread.sleep(2000);
        
        // Select Android platform
        logger.info("📱 Selecting Android platform...");
        sendMcpRequest("tools/call", "{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"android\"}}");
        Thread.sleep(2000);
        
        logger.info("✅ MCP connection established successfully!");
    }
    
    private static void runDemoScenarios() throws IOException, InterruptedException {
        
        // PHASE 2: App Launch and Setup
        logger.info("");
        logger.info("📱 PHASE 2: App Launch and Setup");
        logger.info("===============================");
        
        logger.info("🚀 Creating Android session with SauceLabs demo app...");
        String sessionCapabilities = "{"
            + "\"platform\":\"android\","
            + "\"capabilities\":{"
                + "\"platformName\":\"Android\","
                + "\"deviceName\":\"emulator-5554\","
                + "\"app\":\"" + APK_PATH.replace("\\", "\\\\") + "\","
                + "\"appPackage\":\"" + PACKAGE_NAME + "\","
                + "\"appActivity\":\"" + ACTIVITY_NAME + "\","
                + "\"automationName\":\"UiAutomator2\","
                + "\"newCommandTimeout\":300,"
                + "\"autoGrantPermissions\":true,"
                + "\"noReset\":false,"
                + "\"fullReset\":false"
            + "}"
        + "}";
        
        sendMcpRequest("tools/call", "{\"name\":\"create_session\",\"arguments\":" + sessionCapabilities + "}");
        
        logger.info("⏳ Waiting for app to fully launch...");
        Thread.sleep(15000);
        
        logger.info("📸 Taking initial screenshot...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // PHASE 3: AI-Powered Element Discovery
        logger.info("");
        logger.info("🔍 PHASE 3: AI-Powered Element Discovery");
        logger.info("======================================");
        
        logger.info("🤖 Using AI to discover all interactive elements...");
        sendMcpRequest("tools/call", "{\"name\":\"generate_locators\",\"arguments\":{}}");
        Thread.sleep(5000);
        
        // PHASE 4: Product Catalog Exploration
        logger.info("");
        logger.info("🛒 PHASE 4: Product Catalog Exploration");
        logger.info("=====================================");
        
        logger.info("🔍 Exploring product catalog...");
        logger.info("   📦 Looking for Sauce Labs Backpack...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[contains(@text,'Sauce Labs Backpack')]\"}}}");
        Thread.sleep(3000);
        
        logger.info("   🚴 Looking for Sauce Labs Bike Light...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[contains(@text,'Sauce Labs Bike Light')]\"}}}");
        Thread.sleep(3000);
        
        logger.info("   🔄 Looking for sort functionality...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[contains(@content-desc,'sort') or contains(@text,'sort')]\"}}}");
        Thread.sleep(3000);
        
        // PHASE 5: Shopping Flow Demo
        logger.info("");
        logger.info("🛍️ PHASE 5: Complete Shopping Flow");
        logger.info("=================================");
        
        logger.info("🖱️ Clicking on Sauce Labs Backpack...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_click_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[contains(@text,'Sauce Labs Backpack')]\"}}}");
        Thread.sleep(4000);
        
        logger.info("📸 Taking screenshot of product details...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        logger.info("🛒 Adding product to cart...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_click_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[contains(@text,'ADD TO CART') or contains(@text,'Add To Cart')]\"}}}");
        Thread.sleep(4000);
        
        logger.info("📸 Taking screenshot after adding to cart...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // PHASE 6: User Authentication Demo
        logger.info("");
        logger.info("🔐 PHASE 6: User Authentication Flow");
        logger.info("==================================");
        
        logger.info("🍔 Opening navigation menu...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_click_element\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"open menu\"}}}");
        Thread.sleep(4000);
        
        logger.info("📸 Taking screenshot of menu...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        logger.info("🔐 Navigating to login...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_click_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@text='Log In']\"}}}");
        Thread.sleep(4000);
        
        logger.info("📸 Taking screenshot of login screen...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        logger.info("👤 Entering username...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_send_keys\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"Username input field\",\"text\":\"standard_user\"}}}");
        Thread.sleep(3000);
        
        logger.info("🔑 Entering password...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_send_keys\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"Password input field\",\"text\":\"secret_sauce\"}}}");
        Thread.sleep(3000);
        
        logger.info("🔐 Clicking login button...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_click_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@text='LOGIN']\"}}}");
        Thread.sleep(5000);
        
        // PHASE 7: Final Verification
        logger.info("");
        logger.info("✅ PHASE 7: Final Verification");
        logger.info("=============================");
        
        logger.info("📸 Taking final screenshot to verify successful login...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        logger.info("🔍 Verifying we're back on the main screen...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[contains(@text,'PRODUCTS')]\"}}}");
        Thread.sleep(3000);
        
        logger.info("🛒 Checking cart badge for items...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[contains(@text,'1')]\"}}}");
        Thread.sleep(3000);
    }
    
    private static void startOutputReaders() {
        Thread outputThread = new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    logger.info("📥 MCP: {}", line);
                }
            } catch (IOException e) {
                logger.debug("Output reader finished");
            }
        });
        
        Thread errorThread = new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    logger.debug("🔧 MCP Debug: {}", line);
                }
            } catch (IOException e) {
                logger.debug("Error reader finished");
            }
        });
        
        outputThread.setDaemon(true);
        errorThread.setDaemon(true);
        outputThread.start();
        errorThread.start();
    }
    
    private static void sendMcpRequest(String method, String params) throws IOException {
        String request = "{\"jsonrpc\":\"2.0\",\"id\":\"" + (requestId++) + "\",\"method\":\"" + method + "\",\"params\":" + params + "}";
        logger.debug("📤 Sending: {}", request);
        writer.write(request);
        writer.newLine();
        writer.flush();
    }
}
