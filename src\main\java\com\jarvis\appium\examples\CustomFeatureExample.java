package com.jarvis.appium.examples;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.jarvis.appium.client.JarvisAppiumClient;
import com.jarvis.appium.mcp.model.ToolResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * Example showing how to add custom features to JarvisAppiumClient
 */
public class CustomFeatureExample {
    
    private static final Logger logger = LoggerFactory.getLogger(CustomFeatureExample.class);
    
    public static void main(String[] args) {
        try {
            // Create extended client with custom features
            try (ExtendedJarvisAppiumClient client = new ExtendedJarvisAppiumClient()) {
                client.connect();
                
                // Use custom features
                client.customSwipeGesture(100, 100, 500, 500, 1000);
                client.customWaitForElement("xpath", "//button[@text='Submit']", 10);
                String customData = client.customGetDeviceInfo();
                
                logger.info("Custom device info: {}", customData);
            }
            
        } catch (Exception e) {
            logger.error("Custom feature example failed", e);
        }
    }
    
    /**
     * Extended client with custom features
     */
    public static class ExtendedJarvisAppiumClient extends JarvisAppiumClient {
        
        private final ObjectMapper objectMapper = new ObjectMapper();
        
        /**
         * Custom feature: Perform swipe gesture
         */
        public void customSwipeGesture(int startX, int startY, int endX, int endY, int duration) throws IOException {
            ObjectNode params = objectMapper.createObjectNode();
            params.put("startX", startX);
            params.put("startY", startY);
            params.put("endX", endX);
            params.put("endY", endY);
            params.put("duration", duration);
            
            // Call custom tool on MCP server
            ToolResult result = getMcpClient().callTool("custom_swipe_gesture", params);
            if (result.getIsError() != null && result.getIsError()) {
                throw new IOException("Custom swipe failed: " + result.getTextContent());
            }
            
            logger.info("Custom swipe gesture completed");
        }
        
        /**
         * Custom feature: Wait for element with timeout
         */
        public String customWaitForElement(String strategy, String selector, int timeoutSeconds) throws IOException {
            ObjectNode params = objectMapper.createObjectNode();
            params.put("strategy", strategy);
            params.put("selector", selector);
            params.put("timeout", timeoutSeconds);
            
            ToolResult result = getMcpClient().callTool("custom_wait_for_element", params);
            if (result.getIsError() != null && result.getIsError()) {
                throw new IOException("Custom wait failed: " + result.getTextContent());
            }
            
            logger.info("Element found after waiting");
            return result.getTextContent();
        }
        
        /**
         * Custom feature: Get comprehensive device information
         */
        public String customGetDeviceInfo() throws IOException {
            ObjectNode params = objectMapper.createObjectNode();
            
            ToolResult result = getMcpClient().callTool("custom_get_device_info", params);
            if (result.getIsError() != null && result.getIsError()) {
                throw new IOException("Failed to get device info: " + result.getTextContent());
            }
            
            return result.getTextContent();
        }
        
        /**
         * Access to underlying MCP client for advanced usage
         */
        protected com.jarvis.appium.mcp.client.McpClient getMcpClient() {
            // Use reflection or make mcpClient protected in parent class
            try {
                java.lang.reflect.Field field = JarvisAppiumClient.class.getDeclaredField("mcpClient");
                field.setAccessible(true);
                return (com.jarvis.appium.mcp.client.McpClient) field.get(this);
            } catch (Exception e) {
                throw new RuntimeException("Failed to access MCP client", e);
            }
        }
    }
}
