package com.jarvis.appium.examples;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.concurrent.TimeUnit;

/**
 * Test that actually interacts with Android emulator to make screen changes visible
 */
public class AndroidInteractionTest {
    
    private static final Logger logger = LoggerFactory.getLogger(AndroidInteractionTest.class);
    
    private static BufferedWriter writer;
    private static BufferedReader reader;
    private static BufferedReader errorReader;
    
    public static void main(String[] args) {
        try {
            testAndroidInteractions();
        } catch (Exception e) {
            logger.error("Android interaction test failed", e);
        }
    }
    
    private static void testAndroidInteractions() throws IOException, InterruptedException {
        logger.info("=== 📱 Android Emulator Interaction Test ===");
        logger.info("This test will make visible changes to your Android emulator screen!");
        
        // Start MCP server
        String[] command = {"node", "dist/index.js"};
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.directory(new File("C:\\POC\\AI\\MCP Integration Server\\jarvis-appium"));
        processBuilder.redirectErrorStream(false);
        
        logger.info("🚀 Starting MCP server...");
        Process process = processBuilder.start();
        
        writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
        reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        
        // Start output readers
        startOutputReaders();
        Thread.sleep(3000);
        
        // Initialize MCP
        logger.info("🔗 Initializing MCP connection...");
        sendMcpRequest("initialize", "{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"android-interaction-test\",\"version\":\"1.0.0\"},\"capabilities\":{}}");
        Thread.sleep(2000);
        
        // Select Android platform
        logger.info("📱 Selecting Android platform...");
        sendMcpRequest("tools/call", "{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"android\"}}");
        Thread.sleep(2000);
        
        // Create session
        logger.info("🎯 Creating Android session...");
        String sessionCapabilities = "{\"platform\":\"android\",\"capabilities\":{\"platformName\":\"Android\",\"deviceName\":\"emulator-5554\",\"automationName\":\"UiAutomator2\"}}";
        sendMcpRequest("tools/call", "{\"name\":\"create_session\",\"arguments\":" + sessionCapabilities + "}");
        Thread.sleep(10000);
        
        // Take initial screenshot
        logger.info("📸 Taking initial screenshot...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // Try to open Settings app
        logger.info("⚙️ Attempting to open Settings app...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@text='Settings' or @content-desc='Settings']\"}}");
        Thread.sleep(3000);
        
        // If Settings not found, try to open app drawer first
        logger.info("📋 Opening app drawer...");
        // Swipe up from bottom to open app drawer
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@resource-id='com.google.android.apps.nexuslauncher:id/apps_view']\"}}");
        Thread.sleep(2000);
        
        // Try to find and click on any visible app icon
        logger.info("🔍 Looking for any clickable app...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@clickable='true' and @class='android.widget.TextView']\"}}");
        Thread.sleep(3000);
        
        // Take another screenshot to show changes
        logger.info("📸 Taking final screenshot...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
        
        // Try some basic interactions
        logger.info("👆 Performing basic touch interactions...");
        
        // Try to find the home button or any system UI element
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@resource-id='android:id/navigationBarBackground']\"}}");
        Thread.sleep(2000);
        
        // Try to find any button or clickable element
        sendMcpRequest("tools/call", "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[@clickable='true'][1]\"}}");
        Thread.sleep(2000);
        
        logger.info("✅ Android interaction test completed!");
        logger.info("Check your Android emulator - you should have seen some activity!");
        
        // Clean up
        logger.info("🧹 Cleaning up...");
        writer.close();
        reader.close();
        errorReader.close();
        process.destroy();
    }
    
    private static void sendMcpRequest(String method, String params) throws IOException {
        String request = "{\"jsonrpc\":\"2.0\",\"id\":\"" + System.currentTimeMillis() + "\",\"method\":\"" + method + "\",\"params\":" + params + "}";
        logger.info("📤 SENDING: {}", request);
        writer.write(request);
        writer.newLine();
        writer.flush();
    }
    
    private static void startOutputReaders() {
        Thread outputThread = new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    // Only show important responses, not all debug info
                    if (line.contains("\"result\"") || line.contains("screenshot") || line.contains("Element id") || line.contains("session created")) {
                        logger.info("📥 SERVER: {}", line);
                    }
                }
            } catch (IOException e) {
                // Expected when process ends
            }
        });
        outputThread.setDaemon(true);
        outputThread.start();
        
        Thread errorThread = new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    // Only show important errors
                    if (line.contains("ERROR") && !line.contains("DEBUG")) {
                        logger.error("⚠️ SERVER ERROR: {}", line);
                    }
                }
            } catch (IOException e) {
                // Expected when process ends
            }
        });
        errorThread.setDaemon(true);
        errorThread.start();
    }
}
